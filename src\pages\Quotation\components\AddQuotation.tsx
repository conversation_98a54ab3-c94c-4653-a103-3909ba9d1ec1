import React, { useEffect, useState } from "react";
import {
  Form,
  Input,
  Button,
  message,
  Card,
  Space,
  DatePicker,
  InputNumber,
  Switch,
  Select,
  Row,
  Col,
  Alert,
} from "antd";
import "./index.less";
import { LeftOutlined } from "@ant-design/icons";
import { addInquiry, updateInquiry } from "../services";
import { FormInstance } from "antd/es/form";
import { useAppSelector } from "@/store/hooks";
import dayjs from "dayjs";
import { useWatch } from "antd/es/form/Form";
import useBaseData from "@/hooks/useBaseData";
import { useTranslation } from "react-i18next";
import { formatDateToTimestamp, getCurrentTimestamp } from "@/utils/util";

const FREIGHT_METHOD_OPTIONS = [{ label: "AF", value: "AF" }];

const GOODS_TYPE_OPTIONS = [
  { label: "FCL", value: "FCL" },
  { label: "LCL", value: "LCL" },
];

const TRADE_TERMS_OPTIONS = [
  { label: "EXW", value: "EXW" },
  { label: "FCA", value: "FCA" },
  { label: "FOB", value: "FOB" },
];

interface AddQuotationType {
  open: boolean;
  setOpen: React.Dispatch<React.SetStateAction<boolean>>;
  method: string;
  form: FormInstance;
  setQuotationList: (options?: {
    userId?: number | null;
    pageParams?: any;
    tabKey?: string;
    filterParams?: any;
    resetFilters?: boolean;
  }) => Promise<void>;
  prefilledData?: any;
  inquiryRecords?: any[];
  isMultipleRecords?: boolean;
}
const AddQuotation: React.FC<AddQuotationType> = (props) => {
  const {
    setOpen,
    method,
    form,
    setQuotationList,
    prefilledData,
    inquiryRecords,
    isMultipleRecords,
  } = props;
  const { user } = useAppSelector((state) => state.user);
  const isValidity = useWatch("isvalidity", form);
  const { t } = useTranslation();
  const [isCreatingMultiple, setIsCreatingMultiple] = useState(false);
  const {
    portOptions: portList,
    shipmentPlaceOptions: shipmentPlaceList,
    packageTypeOptions: packageTypeList,
    specialItemOptions: specialItemsList,
    loadAll,
  } = useBaseData();

  // 通用数据格式化
  const formatInquiryData = (values: any, additionalData: any = {}) => {
    return {
      ...values,
      ...additionalData,
      inquirytime: formatDateToTimestamp(values?.inquirytime || dayjs()),
      shipmentdate: values?.shipmentdate
        ? formatDateToTimestamp(dayjs(values?.shipmentdate))
        : undefined,
      freightmethod: values?.freightmethod || "AF",
      userid: user?.userid,
      inquirycreatetime: getCurrentTimestamp(),
      departmentid: user?.departmentid,
      specialcargo: values?.specialcargo
        ? Array.isArray(values.specialcargo)
          ? values.specialcargo.join(",")
          : values.specialcargo
        : undefined,
      shippedplace: values?.shippedplace
        ? Array.isArray(values.shippedplace)
          ? values.shippedplace.join(",")
          : values.shippedplace
        : undefined,
      originport: values?.originport
        ? Array.isArray(values.originport)
          ? values.originport.join("/")
          : values.originport
        : undefined,
      singlemaxweight: values?.singlemaxweight || null,
    };
  };

  const handleSubmitWithErrorHandling = async (
    submitFn: () => Promise<any>,
    successMessage: string,
    errorMessage: string
  ) => {
    try {
      const result = await submitFn();
      if (result?.data?.resultCode === 200) {
        message.success(successMessage);
        // 新增操作重置筛选状态，编辑操作保持筛选状态
        const shouldResetFilters = method === "add";
        setQuotationList({
          userId: user?.userid,
          resetFilters: shouldResetFilters,
        });
        onClose();
        return true;
      } else {
        message.error(
          `${errorMessage}，` +
            (result?.data?.message || t("common.unknownerror"))
        );
        return false;
      }
    } catch (error) {
      console.error("提交失败:", error);
      message.error(`${errorMessage}，${t("common.checkNetworkConnection")}`);
      return false;
    }
  };

  useEffect(() => {
    loadAll();
  }, []);

  useEffect(() => {
    if (prefilledData && method === "add") {
      // 延迟填充表单，确保基础数据已加载
      setTimeout(() => {
        form.setFieldsValue(prefilledData);
      }, 200);
    }
  }, [prefilledData, method, form, isMultipleRecords, inquiryRecords]);
  const goBack = () => {
    setOpen(false);
  };

  const TitleBar = () => {
    return (
      <div className="fixed-title-box">
        <div className="title">
          {`${method === "add" ? t("common.add") : t("common.edit")}${t("common.inquiry")}`}
        </div>
        <div className="go_back" onClick={goBack}>
          <LeftOutlined />
          <span>{t("common.back")}</span>
        </div>
      </div>
    );
  };

  const FormField = ({
    name,
    label,
    rules,
    children,
    colProps = { xs: 24, sm: 24, md: 4 },
    ...formItemProps
  }: any) => (
    <Col {...colProps}>
      <Form.Item name={name} label={label} rules={rules} {...formItemProps}>
        {children}
      </Form.Item>
    </Col>
  );
  const SwitchField = ({
    name,
    label,
    colProps = { xs: 24, sm: 6, md: 6 },
  }: any) => (
    <FormField
      name={name}
      label={label}
      valuePropName="checked"
      colProps={colProps}
    >
      <Switch />
    </FormField>
  );

  const onClose = () => {
    setOpen(false);
  };
  const handleCreateMultipleInquiries = async () => {
    if (!inquiryRecords || inquiryRecords.length === 0) {
      message.error("没有可创建的询价记录");
      return;
    }

    try {
      setIsCreatingMultiple(true);
      const values = await form.validateFields();
      const updateRecordWithFormValues = (
        record: any,
        values: any,
        isFirstRecord: boolean = false
      ) => {
        if (isFirstRecord) {
          // 第一条记录使用表单中的所有数据
          return { ...record, ...values };
        } else {
          // 其他记录只更新公共字段，保留原有的目的港
          return { ...record, ...values, unloadingport: record?.unloadingport };
        }
      };

      const recordsToCreate = inquiryRecords.map((record, index) =>
        updateRecordWithFormValues(record, values, index === 0)
      );

      const promises = recordsToCreate.map(async (data) => {
        const formattedData = formatInquiryData(data);
        return addInquiry(formattedData);
      });

      const results = await Promise.all(promises);
      const allSuccess = results.every((res) => res.data?.resultCode === 200);

      if (!allSuccess) {
        throw new Error(t("emailExtractModal.messages.createFailed"));
      }

      const successMessage =
        recordsToCreate.length > 1
          ? t("emailExtractModal.messages.createMultipleSuccess", {
              count: recordsToCreate.length,
            })
          : t("emailExtractModal.messages.createSuccess");

      message.success(successMessage);
      setQuotationList({ userId: user?.userid, resetFilters: true });
      onClose();
    } catch (error) {
      console.error("批量创建询价失败:", error);
      message.error(t("emailExtractModal.messages.createFailed"));
    } finally {
      setIsCreatingMultiple(false);
    }
  };

  const handleSubmit = async () => {
    try {
      const values = await form.validateFields();
      const formattedValues = formatInquiryData(values);

      const submitFn = () =>
        method === "add"
          ? addInquiry(formattedValues)
          : updateInquiry(formattedValues);

      const successMessage = `${method === "add" ? t("common.add") : t("common.edit")}${t("common.success")}`;
      const errorMessage = `${method === "add" ? t("common.add") : t("common.edit")}${t("common.failure")}`;

      await handleSubmitWithErrorHandling(
        submitFn,
        successMessage,
        errorMessage
      );
    } catch (e) {
      console.log(t("common.formValidationError"), e);
      message.error(t("common.formValidationFailed"));
    }
  };

  return (
    <div className="add-container-quotation">
      {/* 固定在顶部的标题栏 */}
      <TitleBar />

      {/* 主内容区域 */}
      <div className="content-container">
        <Card variant="borderless" className="form-container">
          {/* 多条记录提示 */}
          {isMultipleRecords && inquiryRecords && inquiryRecords.length > 1 && (
            <Alert
              message={t("emailExtractModal.alerts.multiplePortsDetected", {
                count: inquiryRecords.length,
              })}
              description={
                <div>
                  <p>
                    {t("emailExtractModal.alerts.portListLabel")}
                    {inquiryRecords
                      .map((record) => record.unloadingport)
                      .join(", ")}
                  </p>
                  <div>{t("emailExtractModal.alerts.editFirstRecordTip")}</div>
                </div>
              }
              type="info"
              showIcon
              style={{ marginBottom: 16 }}
            />
          )}

          <Form
            form={form}
            layout="vertical"
            onFinish={handleSubmit}
            initialValues={{
              createtime: dayjs(),
              inquirytime: dayjs(),
              inquirycreatetime: dayjs(),
              inquirystate: 0,
            }}
            scrollToFirstError={{ behavior: "smooth", block: "center" }}
          >
            <Form.Item label="ID" name="inquiryid" style={{ display: "none" }}>
              <Input disabled />
            </Form.Item>
            <Card title={t("common.basicinformation")} className="form-section">
              <Row gutter={[24, 0]}>
                <FormField
                  name="inquirytime"
                  label={t("menu.inquiryTime")}
                  rules={[
                    {
                      required: true,
                      message: t("common.pleaseSelectInquiryTime"),
                    },
                  ]}
                >
                  <DatePicker
                    className="full-width"
                    format="YYYY-MM-DD"
                    placeholder={t("common.pleaseSelectInquiryTime")}
                  />
                </FormField>
                <FormField name="originport" label={t("common.originport")}>
                  <Select
                    options={portList}
                    placeholder={t("supplyPrice.placeholders.selectOriginPort")}
                    showSearch
                    optionFilterProp="label"
                    mode="multiple"
                  />
                </FormField>
                <FormField
                  name="unloadingport"
                  label={t("common.destinationport")}
                  rules={[
                    {
                      required: true,
                      message: t("common.pleaseSelectDestinationPort"),
                    },
                  ]}
                >
                  <Select
                    options={portList}
                    placeholder={t(
                      "supplyPrice.placeholders.selectDestinationPort"
                    )}
                    showSearch
                    optionFilterProp="label"
                  />
                </FormField>
                <FormField name="companyname" label={t("common.companyname")}>
                  <Input
                    placeholder={t("supplyPrice.placeholders.inputcompanyname")}
                    maxLength={200}
                  />
                </FormField>
                <FormField name="inquirer" label={t("common.inquirer")}>
                  <Input
                    placeholder={t("supplyPrice.placeholders.inputinquirer")}
                    maxLength={200}
                  />
                </FormField>
                <FormField
                  name="shippedplace"
                  label={t("supplyPrice.fields.shippedPlace")}
                >
                  <Select
                    options={shipmentPlaceList}
                    placeholder={t(
                      "supplyPrice.placeholders.inputShippedPlace"
                    )}
                    mode="tags"
                    showSearch
                    optionFilterProp="label"
                  />
                </FormField>
                <FormField
                  name="freightmethod"
                  label={t("common.shippingmethod")}
                  initialValue={"AF"}
                >
                  <Select
                    options={FREIGHT_METHOD_OPTIONS}
                    placeholder={t(
                      "supplyPrice.placeholders.selectFreightMethod"
                    )}
                    showSearch
                    optionFilterProp="label"
                  />
                </FormField>
                <FormField name="goodstype" label={t("common.cargotype")}>
                  <Select
                    options={GOODS_TYPE_OPTIONS}
                    placeholder={t("supplyPrice.placeholders.selectGoodsType")}
                    showSearch
                    optionFilterProp="label"
                  />
                </FormField>
                <FormField
                  name="tradeterms"
                  label={t("common.tradeterms")}
                  initialValue={"EXW"}
                >
                  <Select
                    options={TRADE_TERMS_OPTIONS}
                    placeholder={t("supplyPrice.placeholders.inputtradeterms")}
                    showSearch
                    optionFilterProp="label"
                  />
                </FormField>
              </Row>
            </Card>

            <Card title={t("common.cargoinformation")} className="form-section">
              <Row gutter={[24, 0]}>
                <FormField
                  name="goodsnumber"
                  label={t("supplyPrice.fields.NumPackages")}
                  colProps={{ xs: 24, sm: 12, md: 6 }}
                >
                  <Input
                    placeholder={t("supplyPrice.placeholders.inputnumpackages")}
                    maxLength={200}
                  />
                </FormField>
                <FormField
                  name="grossweight"
                  label={t("supplyPrice.fields.grossWeight")}
                  rules={[
                    {
                      required: true,
                      message: t("common.pleaseInputGrossWeight"),
                    },
                  ]}
                  colProps={{ xs: 24, sm: 12, md: 6 }}
                >
                  <InputNumber
                    className="full-width"
                    min={0}
                    placeholder={t("supplyPrice.placeholders.inputGrossWeight")}
                    precision={3}
                    step={0.01}
                  />
                </FormField>
                <FormField
                  name="goodsvolume"
                  label={t("supplyPrice.fields.goodsVolume")}
                  colProps={{ xs: 24, sm: 12, md: 6 }}
                >
                  <InputNumber
                    className="full-width"
                    min={0}
                    placeholder={t("supplyPrice.placeholders.inputGoodsVolume")}
                    precision={3}
                    step={0.01}
                  />
                </FormField>
                <FormField
                  name="singlemaxweight"
                  label={t("supplyPrice.fields.singleMaxWeight")}
                  colProps={{ xs: 24, sm: 12, md: 6 }}
                >
                  <InputNumber
                    className="full-width"
                    min={0}
                    placeholder={t(
                      "supplyPrice.placeholders.inputSingleMaxWeight"
                    )}
                    precision={3}
                    step={0.01}
                  />
                </FormField>
              </Row>

              <Row gutter={[24, 0]}>
                <FormField
                  name="cargolength"
                  label={`${t("priceCard.fields.length")}(cm)`}
                  colProps={{ xs: 24, sm: 8, md: 8 }}
                >
                  <InputNumber
                    className="full-width"
                    min={0}
                    placeholder={t("priceCard.placeholders.length")}
                    precision={3}
                  />
                </FormField>
                <FormField
                  name="cargowidth"
                  label={`${t("priceCard.fields.width")}(cm)`}
                  colProps={{ xs: 24, sm: 8, md: 8 }}
                >
                  <InputNumber
                    className="full-width"
                    min={0}
                    placeholder={t("priceCard.placeholders.width")}
                    precision={3}
                  />
                </FormField>
                <FormField
                  name="cargoheight"
                  label={`${t("priceCard.fields.height")}(cm)`}
                  colProps={{ xs: 24, sm: 8, md: 8 }}
                >
                  <InputNumber
                    className="full-width"
                    min={0}
                    placeholder={t("priceCard.placeholders.height")}
                    precision={3}
                  />
                </FormField>
              </Row>
            </Card>

            <Card
              title={t("common.shippingrequirements")}
              className="form-section"
            >
              <Row gutter={[24, 0]}>
                <SwitchField
                  name="isbrand"
                  label={t("supplyPrice.fields.isBrand")}
                />
                <SwitchField
                  name="ensurecabin"
                  label={t("supplyPrice.fields.ensureCabin")}
                />
                <SwitchField
                  name="sanctioned"
                  label={t("supplyPrice.fields.sanctioned")}
                />
                <SwitchField
                  name="isvalidity"
                  label={t("supplyPrice.fields.requireETD")}
                />
              </Row>

              {isValidity && (
                <Row gutter={[24, 0]}>
                  <Col xs={24} sm={12} md={12}>
                    <Form.Item
                      name="shipmentdate"
                      label={t("supplyPrice.fields.shippingDate")}
                      rules={[
                        {
                          required: true,
                          message: t("common.pleaseSelectShipmentDate"),
                        },
                      ]}
                    >
                      <DatePicker
                        className="full-width"
                        format="YYYY-MM-DD"
                        placeholder={t(
                          "supplyPrice.placeholders.selectShipmentDate"
                        )}
                      />
                    </Form.Item>
                  </Col>
                </Row>
              )}
            </Card>

            <Card
              title={t("common.specialrequirements")}
              className="form-section"
            >
              <Row gutter={[24, 0]}>
                <FormField
                  name="specialcargo"
                  label={t("common.specialcargo")}
                  colProps={{ xs: 24, sm: 24, md: 12 }}
                >
                  <Select
                    mode="multiple"
                    options={specialItemsList}
                    placeholder={t(
                      "supplyPrice.placeholders.selectSpecialCargo"
                    )}
                    optionFilterProp="label"
                    maxTagCount={5}
                  />
                </FormField>
                <FormField
                  name="packagetype"
                  label={t("supplyPrice.fields.packageType")}
                  colProps={{ xs: 24, sm: 24, md: 12 }}
                >
                  <Select
                    options={packageTypeList}
                    placeholder={t(
                      "supplyPrice.placeholders.selectPackageType"
                    )}
                    showSearch
                    allowClear
                    optionFilterProp="label"
                  />
                </FormField>
              </Row>
            </Card>

            <Form.Item name="inquirystate" hidden initialValue={0}>
              <InputNumber />
            </Form.Item>
            <Form.Item name="inquirycreatetime" hidden>
              <DatePicker />
            </Form.Item>

            {/* 底部固定按钮 */}
            <div className="fixed-bottom">
              <Space>
                <Button onClick={() => form.resetFields()}>
                  {t("common.reset")}
                </Button>
                <Button onClick={onClose}>{t("common.cancel")}</Button>
                {isMultipleRecords &&
                inquiryRecords &&
                inquiryRecords.length > 1 ? (
                  <Button
                    type="primary"
                    onClick={handleCreateMultipleInquiries}
                    loading={isCreatingMultiple}
                  >
                    {isCreatingMultiple
                      ? t("emailExtractModal.buttons.creating")
                      : t("emailExtractModal.buttons.confirmAndCreate")}
                  </Button>
                ) : (
                  <Button type="primary" onClick={() => form.submit()}>
                    {t("common.save")}
                  </Button>
                )}
              </Space>
            </div>
          </Form>
        </Card>
      </div>
    </div>
  );
};

export default AddQuotation;
