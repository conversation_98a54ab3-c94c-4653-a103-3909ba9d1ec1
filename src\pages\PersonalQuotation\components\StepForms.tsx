import React from "react";
import {
  Form,
  Input,
  InputNumber,
  Select,
  DatePicker,
  Button,
  Row,
  Col,
  Checkbox,
} from "antd";
import { MOCK_FREIGHT_METHODS } from "../constants";

const { Option } = Select;

// 步骤内容组件接口
export interface StepContentProps {
  form: any;
  currentStep: number;
  onNext: () => void;
  onPrev: () => void;
  onSubmit: (values: any) => void;
  portList: any[];
  shipmentPlaceList: any[];
  packagetypeList: any[];
  specialItemsList: any[];
  isValidity: boolean;
}

// 步骤1：运输信息
export const TransportInfoStep: React.FC<
  Omit<StepContentProps, "onPrev" | "onSubmit">
> = ({ onNext, portList, shipmentPlaceList, packagetypeList }) => {
  return (
    <div className="step-content">
      <Row gutter={[24, 16]}>
        <Col xs={24} sm={12} md={8}>
          <Form.Item
            name="originport"
            label="起始港"
            rules={[{ required: true, message: "请选择起始港" }]}
          >
            <Select
              options={portList}
              placeholder="请选择起始港"
              showSearch
              optionFilterProp="label"
              mode="multiple"
            />
          </Form.Item>
        </Col>
        <Col xs={24} sm={12} md={8}>
          <Form.Item
            name="unloadingport"
            label="目的港"
            rules={[{ required: true, message: "请选择卸货港" }]}
          >
            <Select
              options={portList}
              placeholder="请选择卸货港"
              showSearch
              allowClear
              optionFilterProp="label"
            />
          </Form.Item>
        </Col>
        <Col xs={24} sm={12} md={8}>
          <Form.Item name="shippedplace" label="发货地">
            <Select
              placeholder="请选择发货地"
              showSearch
              optionFilterProp="children"
              options={shipmentPlaceList}
            />
          </Form.Item>
        </Col>
        <Col xs={24} sm={12} md={8}>
          <Form.Item
            name="grossweight"
            label="毛重 (kg)"
            rules={[{ required: true, message: "请输入毛重" }]}
          >
            <InputNumber
              className="full-width"
              min={0}
              placeholder="请输入货物毛重"
              precision={3}
              step={0.01}
              style={{ width: "100%" }}
            />
          </Form.Item>
        </Col>
        <Col xs={24} sm={12} md={8}>
          <Form.Item name="goodsvolume" label="货物体积 (cbm)">
            <InputNumber
              style={{ width: "100%" }}
              min={0}
              step={0.01}
              precision={3}
              placeholder="请输入货物体积"
            />
          </Form.Item>
        </Col>
        <Col xs={24} sm={12} md={8}>
          <Form.Item name="packagetype" label="包装类型">
            <Select
              options={packagetypeList}
              placeholder="请选择包装类型"
              showSearch
              allowClear
              optionFilterProp="label"
            />
          </Form.Item>
        </Col>
      </Row>
      <div className="step-actions">
        <Button type="primary" onClick={onNext} htmlType="button">
          下一步
        </Button>
      </div>
    </div>
  );
};

// 步骤2：基本信息
export const BasicInfoStep: React.FC<Omit<StepContentProps, "onSubmit">> = ({
  onNext,
  onPrev,
}) => {
  return (
    <div className="step-content">
      <Row gutter={[24, 16]}>
        <Col xs={24} sm={12} md={8}>
          <Form.Item name="freightmethod" label="货运方式" initialValue={"AF"}>
            <Select
              options={[
                { label: "AF(空运)", value: "AF" },
                // { label: "SF", value: "SF" },
                // { label: "RF", value: "RF" },
              ]}
              placeholder="请选择货运方式"
              showSearch
              optionFilterProp="label"
            />
          </Form.Item>
        </Col>
        <Col xs={24} sm={12} md={8}>
          <Form.Item name="goodstype" label="货物类型">
            <Select
              options={[
                { label: "FCL", value: "FCL" },
                { label: "LCL", value: "LCL" },
              ]}
              placeholder="请选择货物类型"
              allowClear
              showSearch
              optionFilterProp="label"
            />
          </Form.Item>
        </Col>
        <Col xs={24} sm={12} md={8}>
          <Form.Item name="tradeterms" label="贸易术语">
            <Input placeholder="请输入贸易术语" maxLength={200} />
          </Form.Item>
        </Col>
        <Col xs={24} sm={12} md={12}>
          <Form.Item name="companyname" label="公司名">
            <Input placeholder="请输入公司名" maxLength={200} />
          </Form.Item>
        </Col>
        <Col xs={24} sm={12} md={12}>
          <Form.Item name="inquirer" label="询价人">
            <Input placeholder="请输入询价人" maxLength={200} />
          </Form.Item>
        </Col>
      </Row>
      <div className="step-actions">
        <Button onClick={onPrev} style={{ marginRight: 16 }} htmlType="button">
          上一步
        </Button>
        <Button type="primary" onClick={onNext} htmlType="button">
          下一步
        </Button>
      </div>
    </div>
  );
};

// 步骤3：附加信息
export const AdditionalInfoStep: React.FC<Omit<StepContentProps, "onNext">> = ({
  onPrev,
  onSubmit,
  form,
  isValidity,
  specialItemsList,
}) => {
  const handleSubmitClick = () => {
    form
      .validateFields()
      .then((values: any) => {
        onSubmit(values);
      })
      .catch((err: any) => {
        console.log("表单验证失败:", err);
      });
  };

  return (
    <div className="step-content">
      <Row gutter={[24, 16]}>
        <Col xs={24} sm={8} md={8}>
          <Form.Item name="cargoLength" label="货物长度 (cm)">
            <InputNumber
              style={{ width: "100%" }}
              min={0}
              step={0.1}
              placeholder="请输入货物长度"
            />
          </Form.Item>
        </Col>
        <Col xs={24} sm={8} md={8}>
          <Form.Item name="cargoWidth" label="货物宽度 (cm)">
            <InputNumber
              style={{ width: "100%" }}
              min={0}
              step={0.1}
              placeholder="请输入货物宽度"
            />
          </Form.Item>
        </Col>
        <Col xs={24} sm={8} md={8}>
          <Form.Item name="cargoHeight" label="货物高度 (cm)">
            <InputNumber
              style={{ width: "100%" }}
              min={0}
              step={0.1}
              placeholder="请输入货物高度"
            />
          </Form.Item>
        </Col>
        <Col xs={24} sm={8} md={8}>
          <Form.Item name="specialcargo" label="特殊货物">
            <Select
              mode="multiple"
              options={specialItemsList}
              placeholder="请输入特殊货物要求"
              optionFilterProp="label"
              maxTagCount={5}
            />
          </Form.Item>
        </Col>
        <Col xs={24} sm={8} md={8}>
          <Form.Item name="isBrand" valuePropName="checked" label="是否品牌货">
            <Checkbox>是品牌货</Checkbox>
          </Form.Item>
        </Col>
        <Col xs={24} sm={8} md={8}>
          <Form.Item name="ensureCabin" valuePropName="checked" label="保舱">
            <Checkbox>需要保舱</Checkbox>
          </Form.Item>
        </Col>
        <Col xs={24} sm={8} md={8}>
          <Form.Item name="isvalidity" valuePropName="checked" label="要求ETD">
            <Checkbox>要求ETD（发货日期）</Checkbox>
          </Form.Item>
        </Col>
        {isValidity && (
          <Col xs={24} sm={12} md={6}>
            <Form.Item name="shipmentDate" label="发货日期">
              <DatePicker
                style={{ width: "100%" }}
                placeholder="选择日期"
                format="YYYY-MM-DD"
              />
            </Form.Item>
          </Col>
        )}
      </Row>
      <div className="step-actions">
        <Button onClick={onPrev} style={{ marginRight: 16 }} htmlType="button">
          上一步
        </Button>
        <Button type="primary" onClick={handleSubmitClick} htmlType="button">
          开始报价查询
        </Button>
      </div>
    </div>
  );
};
